const { sequelize, CashPoolBatch } = require('../models');
const {
  cashPoolBatchPaymentsRepository,
  cashPoolBatchRepository,
  cashPoolStatementDataRepository,
  clientRepository,
} = require('../repositories');
const asyncControllerWrapper = require('../utils/asyncControllerWrapper');
const { getBatchStatus, getCashPoolPaymentsSheetData, getFilterBy, getOrderBy } = require('../utils/cashPoolBatch');
const { jsonToSheet } = require('../utils/documents');
const { NotFoundError, BadRequestError } = require('../utils/ErrorHandler');
const statementUtils = require('../utils/statementUtils');

const orderByEndDate = [[{ model: CashPoolBatch, as: 'batch' }, 'endDate'], ['id']];

const getCashPoolPayments = async (req, res) => {
  const { clientId } = req.user;
  const { offset = 0, limit = 10 } = req.query;
  const order = getOrderBy(req.query.sort) || orderByEndDate;
  const [paymentFilter, batchFilter, creditorFilter, debtorFilter] = getFilterBy(req.query);

  const totalNumberOfPayments = await cashPoolBatchPaymentsRepository.getTotalPaymentsCount({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
  });

  const payments = await cashPoolBatchPaymentsRepository.getCashPoolPayments({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
    order,
    limit,
    offset: offset * limit,
  });

  // Fetch account trails for the payments
  if (payments.length > 0) {
    const participantAccountIds = payments.map(p => p.participantAccount.id);
    const batchIds = payments.map(p => p.batch.id);

    const accountTrails = await cashPoolBatchPaymentsRepository.getCashPoolPaymentAccountTrails({
      participantAccountIds,
      batchIds,
    });

    // Group account trails by participantAccountId and batchId
    const trailsByAccountAndBatch = {};
    accountTrails.forEach(trail => {
      const key = `${trail.participantAccountId}_${trail.cashPoolBatchId}`;
      if (!trailsByAccountAndBatch[key]) {
        trailsByAccountAndBatch[key] = [];
      }
      trailsByAccountAndBatch[key].push(trail);
    });

    // Merge account trails into payment data
    payments.forEach(payment => {
      const key = `${payment.participantAccount.id}_${payment.batch.id}`;
      payment.participantAccount.accountTrails = trailsByAccountAndBatch[key] || [];
    });
  }

  res.json({ payments, totalNumberOfPayments });
};

const exportCashPoolPayments = async (req, res) => {
  // same logic as in getCashPoolPayments
  const { clientId } = req.user;
  const { columns } = req.query;
  const order = getOrderBy(req.query.sort) || orderByEndDate;
  const [paymentFilter, batchFilter, creditorFilter, debtorFilter] = getFilterBy(req.query);

  const client = await clientRepository.getClient(clientId);

  const payments = await cashPoolBatchPaymentsRepository.getCashPoolPayments({
    clientId,
    wherePayment: paymentFilter,
    whereBatch: batchFilter,
    whereCreditor: creditorFilter,
    whereDebtor: debtorFilter,
    order,
  });

  const sheetData = getCashPoolPaymentsSheetData(payments, columns.split(', '), client.name);
  const data = [{ sheetData, sheetName: 'Cash Pool Interest' }];
  const result = jsonToSheet(data);
  res.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.set('Content-Disposition', 'attachment; filename="Cash Pool Interest.xlsx"');
  res.send(result);
};

/** Marks payment as paid and updates status of batch */
const markCashPoolPaymentAsPaid = async (req, res) => {
  const { cashPoolId, paymentId, batchId } = req.params;
  const { clientId } = req.user;
  const { isPaid, valueDate, statementDate } = req.body;

  await sequelize.transaction(async () => {
    const paymentWhere = { cashPoolId, clientId, batchId, paymentId };
    const payment = await cashPoolBatchPaymentsRepository.getCashPoolPayment(paymentWhere);

    if (!payment) throw new NotFoundError('Payment');
    if (isPaid === false && payment.statementData?.cashPoolBatchId != null) {
      throw new BadRequestError('Cannot mark interest as unpublished whose interest is used in batch.');
    }

    await cashPoolBatchPaymentsRepository.updateCashPoolPayment({ id: paymentId }, { isPaid });

    const { interestPayable, interestReceivable, debtor, creditor } = payment;

    const account = interestPayable !== null ? debtor.accounts[0] : creditor.accounts[0];
    const balanceChange = interestPayable !== null ? -interestPayable : interestReceivable;

    if (account.generateInterestStatementData) {
      if (isPaid) {
        await cashPoolStatementDataRepository.addStatementData({
          date: valueDate,
          statementDate: statementDate,
          balanceChange,
          cashPoolAccountId: account.id,
          transactionReferenceNumber: statementUtils.consts.INTEREST,
          statementNumber: statementUtils.consts.INTEREST,
          cashPoolPaymentId: paymentId,
        });
      } else {
        await cashPoolStatementDataRepository.deleteStatement({ cashPoolPaymentId: paymentId });
      }
    }

    const paymentCountData = { clientId, whereBatch: { id: batchId } };
    const [totalBatchPaymentsCount, paidBatchPaymentsCount] = await Promise.all([
      cashPoolBatchPaymentsRepository.getTotalPaymentsCount(paymentCountData),
      cashPoolBatchPaymentsRepository.getPaidPaymentsCount(paymentCountData),
    ]);

    const status = getBatchStatus(paidBatchPaymentsCount, totalBatchPaymentsCount);
    await cashPoolBatchRepository.updateCashPoolBatch(batchId, { status });

    res.send();
  });
};

module.exports = {
  getCashPoolPayments: asyncControllerWrapper(getCashPoolPayments),
  exportCashPoolPayments: asyncControllerWrapper(exportCashPoolPayments),
  markCashPoolPaymentAsPaid: asyncControllerWrapper(markCashPoolPaymentAsPaid),
};

import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  'Cash Pool': true,
  Creditor: true,
  Debtor: true,
  'Date Range': true,
  'Initial Balance': false,
  'Ending Balance': false,
  'Average Balance': false,
  'Effective Annualized Interest Rate': false,
  Currency: true,
  Interest: true,
  'Rate Type': false,
  'Standalone Rate': false,
  'Interest Due': true,
  Status: true,
  'Interest Added': false,
  'External ID': false,
};

export const cashPoolPaymentColumnsSlice = createSlice({
  name: 'cashPoolPaymentColumns',
  initialState,
  reducers: {
    resetForm: () => {
      return initialState;
    },
    setColumns: (state, action) => {
      return { ...state, ...action.payload };
    },
    updateColumns: (_state, action) => {
      return action.payload;
    },
  },
});

export default cashPoolPaymentColumnsSlice.reducer;

export const { resetForm, setColumns, updateColumns } = cashPoolPaymentColumnsSlice.actions;

export const cashPoolPaymentColumns = (state) => state.cashPoolPaymentColumns;
